# -*- coding: utf-8 -*-
"""
测试SQL方法调用是否正确
验证修正后的方法名是否存在
"""

import sys
from pathlib import Path

# 添加项目路径到sys.path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_sql_methods():
    """测试SQL方法是否存在"""
    try:
        print("开始测试SQL方法...")

        # 1. 导入SQL查询类
        from api.query.MemberConsumeSql import MemberConsumeSqlQueries, MemberConsumeCalculator
        print("✓ MemberConsumeSql 导入成功")

        # 2. 检查方法是否存在
        test_start_date = "20231201"
        test_end_date = "20231231"
        test_bid = "test_bid_001"
        test_sid = None

        # 检查基础消费数据SQL构建方法
        if hasattr(MemberConsumeSqlQueries, 'build_dwoutput_consume_base_sql'):
            print("✓ build_dwoutput_consume_base_sql 方法存在")
            try:
                base_sql = MemberConsumeSqlQueries.build_dwoutput_consume_base_sql(
                    test_start_date, test_end_date, test_bid, test_sid
                )
                if base_sql and "total_consume_cash_real" in base_sql:
                    print("✓ 基础消费数据SQL生成成功")
                else:
                    print("✗ 基础消费数据SQL生成失败")
                    return False
            except Exception as e:
                print(f"✗ 基础消费数据SQL生成异常: {e}")
                return False
        else:
            print("✗ build_dwoutput_consume_base_sql 方法不存在")
            return False

        # 检查储值使用详情SQL构建方法
        if hasattr(MemberConsumeSqlQueries, 'build_dwoutput_consume_detail_sql'):
            print("✓ build_dwoutput_consume_detail_sql 方法存在")
            try:
                detail_sql = MemberConsumeSqlQueries.build_dwoutput_consume_detail_sql(
                    test_start_date, test_end_date, test_bid, test_sid
                )
                if detail_sql and "total_prepay_used_real" in detail_sql:
                    print("✓ 储值使用详情SQL生成成功")
                else:
                    print("✗ 储值使用详情SQL生成失败")
                    return False
            except Exception as e:
                print(f"✗ 储值使用详情SQL生成异常: {e}")
                return False
        else:
            print("✗ build_dwoutput_consume_detail_sql 方法不存在")
            return False

        # 检查券交易数据SQL构建方法
        if hasattr(MemberConsumeSqlQueries, 'build_wedatas_coupon_trade_sql'):
            print("✓ build_wedatas_coupon_trade_sql 方法存在")
            try:
                coupon_sql = MemberConsumeSqlQueries.build_wedatas_coupon_trade_sql(
                    test_start_date, test_end_date, test_bid, test_sid
                )
                if coupon_sql and "total_coupon_trade_amount" in coupon_sql:
                    print("✓ 券交易数据SQL生成成功")
                else:
                    print("✗ 券交易数据SQL生成失败")
                    return False
            except Exception as e:
                print(f"✗ 券交易数据SQL生成异常: {e}")
                return False
        else:
            print("✗ build_wedatas_coupon_trade_sql 方法不存在")
            return False

        # 检查数据合并方法
        if hasattr(MemberConsumeCalculator, 'merge_consume_data'):
            print("✓ merge_consume_data 方法存在")
            
            # 测试数据合并
            mock_base_data = {
                'total_consume_cash_real': 50000,  # 500元 * 100
                'total_consume_uv': 10,
                'total_consume_pv': 25
            }
            
            mock_detail_data = {
                'total_prepay_used_real': 30000  # 300元 * 100
            }
            
            mock_coupon_data = {
                'total_coupon_trade_amount': 10000  # 100元 * 100
            }

            try:
                merged_data = MemberConsumeCalculator.merge_consume_data(
                    mock_base_data, mock_detail_data, mock_coupon_data
                )
                
                if merged_data and 'total_real_income' in merged_data:
                    total_real_income = merged_data.get('total_real_income', 0)
                    print(f"✓ 数据合并成功，总实收金额: {total_real_income}")
                else:
                    print("✗ 数据合并失败")
                    return False
            except Exception as e:
                print(f"✗ 数据合并异常: {e}")
                return False
        else:
            print("✗ merge_consume_data 方法不存在")
            return False

        print("🎉 所有SQL方法测试通过！")
        return True

    except Exception as e:
        print(f"SQL方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = test_sql_methods()
    if result:
        print("\n✅ SQL方法修正成功！")
        print("\n📋 修正内容:")
        print("• get_dwoutput_consume_base_data_sql → build_dwoutput_consume_base_sql")
        print("• get_dwoutput_prepay_used_detail_sql → build_dwoutput_consume_detail_sql")
        print("• get_wedatas_coupon_trade_sql → build_wedatas_coupon_trade_sql")
        print("• 所有方法调用现在使用正确的方法名")
    else:
        print("\n❌ SQL方法测试失败！")
        sys.exit(1)
