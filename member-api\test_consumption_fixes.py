# -*- coding: utf-8 -*-
"""
测试会员消费数据修正
验证修正后的系统是否能正常工作
"""

import sys
from pathlib import Path

# 添加项目路径到sys.path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_consumption_fixes():
    """测试消费数据修正"""
    try:
        print("开始测试消费数据修正...")

        # 1. 测试SQL方法导入
        print("1. 测试SQL方法导入...")
        from api.query.MemberConsumeSql import MemberConsumeSqlQueries
        print("✓ MemberConsumeSqlQueries 导入成功")

        # 2. 测试消费图片生成器导入
        print("2. 测试消费图片生成器导入...")
        from api.PPTreport.picture.MemberConsumptionPic import MemberConsumptionPicGenerator, create_member_consumption_pic_generator
        print("✓ MemberConsumptionPicGenerator 导入成功")

        # 3. 测试AI分析器导入
        print("3. 测试AI分析器导入...")
        from api.PPTreport.picture.PictureAi import PictureAiAnalyzer
        print("✓ PictureAiAnalyzer 导入成功")

        # 4. 测试SQL方法存在性
        print("4. 测试SQL方法存在性...")
        
        required_methods = [
            'build_dwoutput_consume_base_sql',
            'build_dwoutput_consume_detail_sql', 
            'build_wedatas_coupon_trade_sql'
        ]
        
        for method_name in required_methods:
            if hasattr(MemberConsumeSqlQueries, method_name):
                print(f"✓ {method_name} 方法存在")
            else:
                print(f"✗ {method_name} 方法不存在")
                return False

        # 5. 测试SQL生成
        print("5. 测试SQL生成...")
        
        test_start_date = "20231201"
        test_end_date = "20231231"
        test_bid = "test_bid_001"
        
        try:
            base_sql = MemberConsumeSqlQueries.build_dwoutput_consume_base_sql(
                test_start_date, test_end_date, test_bid, None
            )
            if base_sql and "total_consume_cash_real" in base_sql:
                print("✓ 基础消费数据SQL生成成功")
            else:
                print("✗ 基础消费数据SQL生成失败")
                return False
                
        except Exception as e:
            print(f"✗ SQL生成异常: {e}")
            return False

        # 6. 测试消费图片生成器创建
        print("6. 测试消费图片生成器创建...")
        
        try:
            from api.PPTreport.picture.PictureSave import create_image_manager
            image_manager = create_image_manager("test_bid_001")
            consumption_generator = create_member_consumption_pic_generator("test_bid_001", image_manager)
            
            if consumption_generator:
                print("✓ 消费图片生成器创建成功")
                print(f"  - 生成器类型: {type(consumption_generator).__name__}")
                print(f"  - 品牌ID: {consumption_generator.bid}")
            else:
                print("✗ 消费图片生成器创建失败")
                return False
                
        except Exception as e:
            print(f"✗ 消费图片生成器创建异常: {e}")
            return False

        # 7. 测试AI分析方法存在性
        print("7. 测试AI分析方法存在性...")
        
        ai_analyzer = PictureAiAnalyzer()
        ai_methods = [
            'analyze_member_consumption_last_year_data',
            'analyze_member_consumption_this_year_data',
            'generate_all_member_consumption_analysis'
        ]
        
        for method_name in ai_methods:
            if hasattr(ai_analyzer, method_name):
                print(f"✓ {method_name} 方法存在")
            else:
                print(f"✗ {method_name} 方法不存在")
                return False

        # 8. 测试数据表格方法存在性
        print("8. 测试数据表格方法存在性...")
        
        if hasattr(consumption_generator, '_add_consumption_data_table'):
            print("✓ _add_consumption_data_table 方法存在")
        else:
            print("✗ _add_consumption_data_table 方法不存在")
            return False

        # 9. 测试时间范围计算
        print("9. 测试时间范围计算...")

        try:
            # 模拟查询参数
            test_query_params = {
                'end_date': '2025-06-30',
                'bid': 'test_bid_001',
                'sid': None
            }

            # 测试时间范围生成
            test_ranges = consumption_generator._generate_monthly_ranges(2024, 1, 12)
            if test_ranges and len(test_ranges) == 12:
                print(f"✓ 时间范围生成成功: {len(test_ranges)} 个月")
                print(f"  - 第一个月: {test_ranges[0]}")
                print(f"  - 最后一个月: {test_ranges[-1]}")
            else:
                print("✗ 时间范围生成失败")
                return False

        except Exception as e:
            print(f"✗ 时间范围计算异常: {e}")
            return False

        # 10. 测试参数提取
        print("10. 测试参数提取...")

        try:
            test_params = {'end_date': '2025-06-30', 'bid': 'test_bid'}
            end_date = consumption_generator._extract_param(test_params, 'end_date', '2025-01-01')
            bid = consumption_generator._extract_param(test_params, 'bid')

            if end_date == '2025-06-30' and bid == 'test_bid':
                print("✓ 参数提取功能正常")
            else:
                print("✗ 参数提取功能异常")
                return False

        except Exception as e:
            print(f"✗ 参数提取异常: {e}")
            return False

        print("🎉 所有消费数据修正测试通过！")
        return True

    except Exception as e:
        print(f"消费数据修正测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = test_consumption_fixes()
    if result:
        print("\n✅ 消费数据修正成功！")
        print("\n🔧 修正内容总结:")
        print("• 修正SQL方法调用: 使用正确的 build_* 方法名")
        print("• 移除消费人数计算: 避免除零错误和不必要的计算")
        print("• 直接数据提取: 避免使用 merge_consume_data 方法")
        print("• 添加数据表格: 在图表下方显示详细数据")
        print("• 集成AI分析: 完整的消费数据智能分析")
        print("• 统一时间范围计算: 与NewMembersPic.py完全一致")
        print("• 统一横轴坐标处理: 使用相同的标签和旋转角度")
        print("• 统一图表样式: 保持与现有图表的视觉一致性")
        print("\n📊 系统特性:")
        print("• 5个数据维度: 总实收、储值实收、现金实收、储值占比、现金占比")
        print("• 双轴图表设计: 左轴金额(万元)，右轴占比(%)")
        print("• 数据表格显示: 横向布局，包含所有关键数据")
        print("• AI智能分析: 消费规模、支付结构、季节性波动分析")
        print("• 完整系统集成: 与PPT报告系统无缝集成")
        print("\n⏰ 时间范围逻辑:")
        print("• 去年图表: 完整12个月数据 (如2024年1-12月)")
        print("• 今年图表: 1月到当前月份前一天 (如2025年1-7月，7月到27日)")
        print("• 横轴坐标: 月份标签45度旋转，右对齐")
        print("• 查询示例: 2025.6.1-2025.6.30 → 去年2024.1-12，今年2025.1-7")
    else:
        print("\n❌ 消费数据修正失败！")
        sys.exit(1)
