from pydantic import BaseModel, Field, ConfigDict
from typing import Any, Optional, Dict, List, Union
from datetime import datetime
from enum import Enum

def to_camel_case(snake_str: str) -> str:
    """将snake_case转换为camelCase"""
    components = snake_str.split('_')
    return components[0] + ''.join(x.capitalize() for x in components[1:])

class ResponseModel(BaseModel):
    """统一响应模型"""
    code: int
    message: str
    data: Optional[Any] = None

class QueryTypeEnum(str, Enum):
    """查询类型枚举"""
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    HALFYEAR = "halfyear"
    CUSTOM = "custom"

class CompareOptionEnum(str, Enum):
    """对比选项枚举"""
    CHAIN = "chain"  # 环比
    YEAR_ON_YEAR = "yearOnYear"  # 同比

class QueryParams(BaseModel):
    """查询参数模型"""
    query_type: QueryTypeEnum = Field(..., description="查询类型")
    bid: str = Field(..., description="品牌ID")
    sid: Optional[str] = Field(None, description="门店ID")
    start_date: Optional[str] = Field(None, description="开始日期 YYYY-MM-DD")
    end_date: Optional[str] = Field(None, description="结束日期 YYYY-MM-DD")
    compare_options: Optional[List[CompareOptionEnum]] = Field(default=[], description="对比选项")
    cashier_system: Optional[str] = Field("0", description="收银系统类型：0-无收银系统，1-品智收银")
    merchant_id: Optional[str] = Field(None, description="商户ID（品智收银门店拼音名称）")

class FieldDataModel(BaseModel):
    """字段数据模型"""
    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True
    )
    
    value: Union[int, float] = Field(0, description="实际值")
    unit: str = Field("", description="单位")
    chain_comparison: List[Union[int, float]] = Field(default=[], description="环比数据")
    chain_change_rate: List[str] = Field(default=[], description="环比变化率")
    chain_labels: List[str] = Field(default=[], description="环比标签")
    year_over_year: Union[int, float] = Field(0, description="同比数据")
    year_over_year_rate: str = Field("0%", description="同比变化率")

class MemberBaseData(BaseModel):
    """会员基础数据模型"""
    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True
    )
    
    total_members: FieldDataModel = Field(default_factory=FieldDataModel, description="会员总数量")
    net_members: FieldDataModel = Field(default_factory=FieldDataModel, description="会员净存量")
    new_members: FieldDataModel = Field(default_factory=FieldDataModel, description="新增会员数量")
    unfollow_members: FieldDataModel = Field(default_factory=FieldDataModel, description="取关会员数量")
    new_unfollow_members: FieldDataModel = Field(default_factory=FieldDataModel, description="新增取关人数")
    unfollow_rate: FieldDataModel = Field(default_factory=FieldDataModel, description="取关占比")
    new_consume_members: FieldDataModel = Field(default_factory=FieldDataModel, description="新增消费的会员数")
    new_charge_members: FieldDataModel = Field(default_factory=FieldDataModel, description="新增储值的会员数")
    total_consume_members: FieldDataModel = Field(default_factory=FieldDataModel, description="累计消费的会员数")
    total_charge_members: FieldDataModel = Field(default_factory=FieldDataModel, description="累计储值的会员数")
    new_complete_members: FieldDataModel = Field(default_factory=FieldDataModel, description="新增完善会员量")
    new_complete_rate: FieldDataModel = Field(default_factory=FieldDataModel, description="新增会员完善率")
    total_complete_members: FieldDataModel = Field(default_factory=FieldDataModel, description="完善会员总数")
    phone_member_ratio: FieldDataModel = Field(default_factory=FieldDataModel, description="完善手机号会员占比")
    prepay_member_ratio: FieldDataModel = Field(default_factory=FieldDataModel, description="储值会员占比")
    consume_member_ratio: FieldDataModel = Field(default_factory=FieldDataModel, description="会员消费人数占比")
    complete_phone_members: FieldDataModel = Field(default_factory=FieldDataModel, description="完善手机号会员数量")
    consume_zero_members: FieldDataModel = Field(default_factory=FieldDataModel, description="会员0次消费总人数")
    consume_once_members: FieldDataModel = Field(default_factory=FieldDataModel, description="会员1次消费总人数")
    consume_multiple_members: FieldDataModel = Field(default_factory=FieldDataModel, description="会员2次消费以上总人数")

class MemberConsumeData(BaseModel):
    """会员消费数据模型"""
    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True
    )

    total_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="消费总金额")
    consume_users: FieldDataModel = Field(default_factory=FieldDataModel, description="会员消费人数")
    total_consume_count: FieldDataModel = Field(default_factory=FieldDataModel, description="会员总消费笔数")
    actual_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="会员实收金额")
    cash_users: FieldDataModel = Field(default_factory=FieldDataModel, description="现金支付会员数")
    cash_consume_count: FieldDataModel = Field(default_factory=FieldDataModel, description="会员现金消费笔数")
    prepay_users: FieldDataModel = Field(default_factory=FieldDataModel, description="储值支付会员数")
    prepay_consume_count: FieldDataModel = Field(default_factory=FieldDataModel, description="会员储值消费笔数")
    prepay_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="会员储值消费金额")
    prepay_actual_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="会员使用储值的实收金额")
    prepay_consumption_ratio: FieldDataModel = Field(default_factory=FieldDataModel, description="储值消费实收金额占比")
    total_actual_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="会员总实收金额")
    first_consume_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="首次消费金额")
    repeat_consume_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="再次消费金额")
    avg_contribution: FieldDataModel = Field(default_factory=FieldDataModel, description="会员人均贡献")
    consume_frequency: FieldDataModel = Field(default_factory=FieldDataModel, description="会员消费频次")
    avg_consume_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="会员单均消费")
    consume_once_members: FieldDataModel = Field(default_factory=FieldDataModel, description="消费1次的会员数量")
    consume_twice_members: FieldDataModel = Field(default_factory=FieldDataModel, description="消费2次的会员数量")
    consume_thrice_members: FieldDataModel = Field(default_factory=FieldDataModel, description="消费3次的会员数量")
    consume_more_than_thrice_members: FieldDataModel = Field(default_factory=FieldDataModel, description="消费3次以上会员数量")
    repurchase_rate: FieldDataModel = Field(default_factory=FieldDataModel, description="复购率")
    coupon_trade_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="券带动总交易金额")

class MemberChargeData(BaseModel):
    """会员充值数据模型"""
    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True
    )

    charge_count: FieldDataModel = Field(default_factory=FieldDataModel, description="期间会员充值笔数")
    charge_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="期间充值实收总金额")
    period_charge_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="期间充值金额")
    period_charge_present: FieldDataModel = Field(default_factory=FieldDataModel, description="期间充值赠送金额")
    period_charge_amount_unused: FieldDataModel = Field(default_factory=FieldDataModel, description="期末储值沉淀金额")
    consume_prepay_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="期间消耗储值实收总金额")
    retention_rate: FieldDataModel = Field(default_factory=FieldDataModel, description="存储留存率")

class CouponItemData(BaseModel):
    """券项目数据模型"""
    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True
    )

    coupon_name: str = Field("", description="券名称")
    coupon_id: str = Field("", description="券编号ID")
    coupon_send_count: int = Field(0, description="券发放量(张)")
    coupon_used_count: int = Field(0, description="券使用量(张)")
    coupon_usage_rate: float = Field(0.0, description="券使用率(%)")
    coupon_discount_amount: float = Field(0.0, description="券抵扣金额(元)")
    drive_prepay_amount: float = Field(0.0, description="带动储值消费(元)")
    drive_cash_amount: float = Field(0.0, description="带动现金消费(元)")
    drive_total_amount: float = Field(0.0, description="带动总交易金额")

class CouponSummaryData(BaseModel):
    """券汇总统计数据"""
    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True
    )

    total_send_count: int = Field(0, description="券发放总量(张)")
    total_used_count: int = Field(0, description="券使用总量(张)")
    avg_usage_rate: float = Field(0.0, description="平均使用率(%)")
    total_drive_amount: float = Field(0.0, description="带动总金额(元)")

class CouponTradeData(BaseModel):
    """券交易数据模型"""
    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True
    )

    items: List[CouponItemData] = Field(default_factory=list, description="券项目列表")
    summary: CouponSummaryData = Field(default_factory=CouponSummaryData, description="汇总统计")

    # 保留原有的汇总字段，用于兼容性
    coupon_members: FieldDataModel = Field(default_factory=FieldDataModel, description="券交易会员数量")
    coupon_frequency: FieldDataModel = Field(default_factory=FieldDataModel, description="券交易频次")
    coupon_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="券交易金额")
    drive_total_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="带动总交易金额")

class PinzhiCashierData(BaseModel):
    """品智收银数据模型"""
    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True
    )

    total_actual_revenue: FieldDataModel = Field(default_factory=FieldDataModel, description="营业额总实收")
    total_expected_revenue: FieldDataModel = Field(default_factory=FieldDataModel, description="营业额总应收")
    discount_rate: FieldDataModel = Field(default_factory=FieldDataModel, description="折扣率")
    dine_in_actual_revenue: FieldDataModel = Field(default_factory=FieldDataModel, description="堂食(非外卖)营业额实收")
    dine_in_order_count: FieldDataModel = Field(default_factory=FieldDataModel, description="堂食(非外卖)订单数")
    takeout_actual_revenue: FieldDataModel = Field(default_factory=FieldDataModel, description="外卖营业额实收")
    takeout_order_count: FieldDataModel = Field(default_factory=FieldDataModel, description="外卖订单数")

    # 新增的6个字段
    brand_avg_order_value: FieldDataModel = Field(default_factory=FieldDataModel, description="品牌平均客单价")
    dine_in_avg_order_value: FieldDataModel = Field(default_factory=FieldDataModel, description="堂食平均客单价")
    takeout_avg_order_value: FieldDataModel = Field(default_factory=FieldDataModel, description="外卖平均客单价")
    dine_in_revenue_ratio: FieldDataModel = Field(default_factory=FieldDataModel, description="堂食实收占比")
    takeout_revenue_ratio: FieldDataModel = Field(default_factory=FieldDataModel, description="外卖实收占比")
    total_order_count: FieldDataModel = Field(default_factory=FieldDataModel, description="订单总数")

    # 会员相关的5个字段
    member_total_actual_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="会员总实收金额")
    non_member_total_actual_amount: FieldDataModel = Field(default_factory=FieldDataModel, description="非会员总实收金额")
    member_dine_in_ratio: FieldDataModel = Field(default_factory=FieldDataModel, description="会员消费金额占堂食实收的比")
    member_total_consume_count: FieldDataModel = Field(default_factory=FieldDataModel, description="会员总消费笔数")
    member_dine_in_count_ratio: FieldDataModel = Field(default_factory=FieldDataModel, description="会员消费单数占堂食单数的比")

class MemberDataResponse(BaseModel):
    """会员数据响应模型"""
    model_config = ConfigDict(
        alias_generator=to_camel_case,
        populate_by_name=True
    )
    
    member_base: MemberBaseData = Field(default_factory=MemberBaseData, description="会员基础数据")
    member_consume: MemberConsumeData = Field(default_factory=MemberConsumeData, description="会员消费数据")
    member_charge: MemberChargeData = Field(default_factory=MemberChargeData, description="会员充值数据")
    coupon_trade: CouponTradeData = Field(default_factory=CouponTradeData, description="券交易数据")
    pinzhi_cashier: PinzhiCashierData = Field(default_factory=PinzhiCashierData, description="品智收银数据")

class AIAnalysisRequest(BaseModel):
    """AI分析请求模型"""
    module_type: str = Field(..., description="模块类型")
    data: Dict[str, Any] = Field(..., description="模块数据")
    config: QueryParams = Field(..., description="查询配置")

class AIAnalysisResponse(BaseModel):
    """AI分析响应模型"""
    analysis_result: str = Field("", description="AI分析结果HTML内容")
    insights: List[str] = Field(default=[], description="数据洞察")
    recommendations: List[str] = Field(default=[], description="建议")

class ExportRequest(BaseModel):
    """导出请求模型"""
    format: str = Field("xlsx", description="导出格式")
    filename: str = Field("", description="文件名")
    data: MemberDataResponse = Field(..., description="导出数据")
    query_params: QueryParams = Field(..., description="查询参数")

class ExportResponse(BaseModel):
    """导出响应模型"""
    download_url: str = Field("", description="下载链接")
    filename: str = Field("", description="文件名")
    file_size: int = Field(0, description="文件大小(字节)")






