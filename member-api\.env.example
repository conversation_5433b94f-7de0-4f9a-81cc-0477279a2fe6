# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# 灵积配置，通义api配置
DASHSCOPE_API_KEY=your_api_key_here
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
DASHSCOPE_MODEL=qwen-max

# MySQL数据库配置 - dwoutput数据库
DWOUTPUT_DB_HOST=localhost
DWOUTPUT_DB_PORT=3306
DWOUTPUT_DB_USER=your_actual_username
DWOUTPUT_DB_PASSWORD=your_actual_password
DWOUTPUT_DB_NAME=dwoutput
DWOUTPUT_DB_CHARSET=utf8mb4

# MySQL数据库配置 - wedatas数据库  
WEDATAS_DB_HOST=localhost
WEDATAS_DB_PORT=3306
WEDATAS_DB_USER=your_actual_username
WEDATAS_DB_PASSWORD=your_actual_password
WEDATAS_DB_NAME=wedatas
WEDATAS_DB_CHARSET=utf8mb4


# PostgreSQL数据库配置 - 品质收银数据库
POS_DW_DB_HOST=your_postgres_host_here
POS_DW_DB_PORT=5432
POS_DW_DB_USER=your_postgres_username_here
POS_DW_DB_PASSWORD=your_postgres_password_here
POS_DW_DB_NAME=pos_dw
POS_DW_DB_CHARSET=utf8

# 本项目OSS配置（用于存储分析结果）
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_BUCKET_NAME=your_oss_bucket_name
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
OSS_REGION=oss-cn-hangzhou
